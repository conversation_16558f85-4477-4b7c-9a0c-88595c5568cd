"""service fields

Revision ID: 2025.06.16.4
Revises: 2025.06.16.3
Create Date: 2025-06-16 17:26:14.552368

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '2025.06.16.4'
down_revision: Union[str, None] = '2025.06.16.3'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('service_components', sa.Column('title', sa.String(), nullable=False))
    op.add_column('service_components', sa.Column('price', sa.Float(), nullable=True))
    op.add_column('service_components', sa.Column('price_type', sa.String(), nullable=True))
    op.add_column('service_components', sa.Column('description', sa.Text(), nullable=True))
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('service_components', 'description')
    op.drop_column('service_components', 'price_type')
    op.drop_column('service_components', 'price')
    op.drop_column('service_components', 'title')
    # ### end Alembic commands ###
