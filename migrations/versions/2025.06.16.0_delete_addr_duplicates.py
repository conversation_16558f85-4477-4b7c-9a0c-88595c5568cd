"""delete addr duplicates

Revision ID: 2025.06.16.0
Revises: 2025.06.12.2
Create Date: 2025-06-16 11:31:17.866813

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '2025.06.16.0'
down_revision: Union[str, None] = '2025.06.12.2'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.execute(
        "DELETE from addresses where exists (select id from addresses ad2 where ad2.ctid > addresses.ctid and ad2.id = addresses.id);"
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    pass
    # ### end Alembic commands ###
