"""components add connection client service id

Revision ID: 2025.06.16.3
Revises: 2025.06.16.2
Create Date: 2025-06-16 14:10:12.745962

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '2025.06.16.3'
down_revision: Union[str, None] = '2025.06.16.2'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('client_service_components', sa.Column('client_service_id', sa.Integer(), nullable=False))
    op.drop_constraint('client_service_components_service_id_fkey', 'client_service_components', type_='foreignkey')
    op.create_foreign_key(None, 'client_service_components', 'client_services', ['client_service_id'], ['id'])
    op.drop_column('client_service_components', 'service_id')
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('client_service_components', sa.Column('service_id', sa.VARCHAR(length=36), autoincrement=False, nullable=False))
    op.drop_constraint(None, 'client_service_components', type_='foreignkey')
    op.create_foreign_key('client_service_components_service_id_fkey', 'client_service_components', 'services', ['service_id'], ['id'])
    op.drop_column('client_service_components', 'client_service_id')
    # ### end Alembic commands ###
