"""client services components cols

Revision ID: 2025.06.16.5
Revises: 2025.06.16.4
Create Date: 2025-06-16 17:45:31.366412

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '2025.06.16.5'
down_revision: Union[str, None] = '2025.06.16.4'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('client_service_components', sa.Column('title', sa.String(), nullable=False))
    op.add_column('client_service_components', sa.Column('price', sa.Float(), nullable=True))
    op.add_column('client_service_components', sa.Column('price_type', sa.String(), nullable=True))
    op.add_column('client_service_components', sa.Column('description', sa.Text(), nullable=True))
    op.drop_constraint('client_service_components_service_component_id_fkey', 'client_service_components', type_='foreignkey')
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_foreign_key('client_service_components_service_component_id_fkey', 'client_service_components', 'service_components', ['service_component_id'], ['id'])
    op.drop_column('client_service_components', 'description')
    op.drop_column('client_service_components', 'price_type')
    op.drop_column('client_service_components', 'price')
    op.drop_column('client_service_components', 'title')
    # ### end Alembic commands ###
