"""components rename service_component_id

Revision ID: 2025.06.16.2
Revises: 2025.06.16.1
Create Date: 2025-06-16 14:06:17.078173

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '2025.06.16.2'
down_revision: Union[str, None] = '2025.06.16.1'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('client_service_components', sa.Column('service_component_id', sa.Integer(), nullable=False))
    op.drop_constraint('client_service_components_client_service_id_fkey', 'client_service_components', type_='foreignkey')
    op.create_foreign_key(None, 'client_service_components', 'service_components', ['service_component_id'], ['id'])
    op.drop_column('client_service_components', 'client_service_id')
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('client_service_components', sa.Column('client_service_id', sa.INTEGER(), autoincrement=False, nullable=False))
    op.drop_constraint(None, 'client_service_components', type_='foreignkey')
    op.create_foreign_key('client_service_components_client_service_id_fkey', 'client_service_components', 'client_services', ['client_service_id'], ['id'])
    op.drop_column('client_service_components', 'service_component_id')
    # ### end Alembic commands ###
