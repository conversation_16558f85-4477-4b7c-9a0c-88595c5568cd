"""unique address id

Revision ID: 2025.06.16.1
Revises: 2025.06.16.0
Create Date: 2025-06-16 11:40:33.389126

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '2025.06.16.1'
down_revision: Union[str, None] = '2025.06.16.0'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_primary_key('addresses_pkey', 'addresses', ['id'])
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint('addresses_pkey', 'addresses', type_='primary')
    # ### end Alembic commands ###
