from sqlalchemy import delete, update, text
from sqlalchemy import exc
from sqlalchemy.future import select

from records.db import base
from records.db import models
from records.utils import utils


@base.session_aware()
async def create_client_service(values, session=None):
    client_service = models.ClientService(**values)

    try:
        session.add(client_service)
    except exc.SQLAlchemyError as e:
        raise RuntimeError(
            "Duplicate entry for ClientService: %s" % e
        )

    return client_service


@base.session_aware()
async def create_service(values, session=None):
    return await base.create_model(models.Service, values, session=session)


@base.session_aware()
async def update_service(service: models.Service, new_values: dict, session=None):
    update_q = update(models.Service).where(models.Service.id == service.id)
    update_q = update_q.values(new_values)

    await session.execute(update_q)

    service.update(new_values)

    return service


@base.session_aware()
async def delete_service(title: str, session=None):
    delete_q = delete(models.Service).where(models.Service.title == title)
    await session.execute(delete_q)


@base.session_aware()
async def update_client_service(client_service: models.ClientService, new_values: dict, session=None):
    new_values['updated_at'] = base.now()
    update_q = update(models.ClientService).where(models.ClientService.id == client_service.id)
    update_q = update_q.values(new_values)

    await session.execute(update_q)

    client_service.update(new_values)

    return client_service


@base.session_aware()
async def delete_client_service(id: int, session=None):
    delete_q = delete(models.ClientService).where(models.ClientService.id == id)
    await session.execute(delete_q)


@base.session_aware()
async def delete_client_services(ids: list[int] = None, client_id: str = None, session=None):
    return await base.delete_client_datas(models.ClientService, ids=ids, client_id=client_id, session=session)


@base.session_aware()
async def list_client_services(
    client_id: str,
    # order: str = 'id',
    # desc: bool = False,
    session=None
):
    offset = None
    # if limit and page:
    #     limit, offset = utils.get_limit_offset(limit, page)

    query = select(models.ClientService, models.Service, models.ClientServiceComponent).where(models.ClientService.client_id == client_id)
    query = query.outerjoin(models.Service, models.ClientService.service_id == models.Service.id)
    query = query.outerjoin(models.ClientServiceComponent, models.ClientService.id == models.ClientServiceComponent.client_service_id)
    # if q:
    #     query = query.where(models.ClientService.name.ilike(f'%{q}%'))

    query = query.order_by(models.ClientService.id, models.ClientServiceComponent.sequence)

    res = await session.execute(query)
    joined_res = res.fetchall()

    client_services = {}
    for client_service, service, client_service_component in joined_res:
        if client_service.id not in client_services:
            client_services[client_service.id] = client_service
            client_services[client_service.id].service = service
            client_services[client_service.id].components = []
        if client_service_component:
            client_services[client_service.id].components.append(client_service_component)

    return list(client_services.values())


@base.session_aware()
async def list_client_services_all(
    ids: list[int] = None,
    session=None
):
    query = select(models.ClientService)
    if ids:
        query = query.where(models.ClientService.id.in_(ids))

    res = await session.execute(query)

    return res.scalars().fetchall()


@base.session_aware()
async def list_services(q: str = None, session=None):
    query = select(models.Service, models.ServiceComponent)
    query = query.outerjoin(models.ServiceComponent, models.Service.id == models.ServiceComponent.service_id)
    query = query.order_by(models.Service.title, models.ServiceComponent.sequence)
    if q:
        query = query.where(models.Service.title.ilike(f'%{q}%'))
    joined_res = await session.execute(query)
    joined_res = joined_res.fetchall()

    services = {}
    for service, component in joined_res:
        if service.id not in services:
            services[service.id] = service
            services[service.id].components = []
        if component:
            services[service.id].components.append(component)

    return list(services.values())


@base.session_aware()
async def list_services_by_title(
    title: list[str],
    session=None
):
    query = select(models.Service).where(models.Service.title.in_(title))
    res = await session.execute(query)

    return res.scalars().fetchall()


@base.session_aware()
async def get_service_by_title(title: str, session=None):
    query = select(models.Service).where(models.Service.title == title)

    res = await session.execute(query)
    res = res.scalar()
    return res


@base.session_aware()
async def get_service_by_id(id: str, session=None):
    return await base.get_by_id(models.Service, id, session=session)


@base.session_aware()
async def get_client_service_by_id(id: int, session=None):
    return await base.get_by_id(models.ClientService, id, session=session)


@base.session_aware()
async def get_client_service_count(
    client_id: str = None,
    q: str = None,
    session=None,
):
    where = []
    params = {}
    if client_id:
        where.append('client_id = :client_id')
        params['client_id'] = client_id
    # if q:
    #     q = f'%{q}%'
    #     where.append('name ILIKE :q')
    #     params['q'] = q

    raw = 'SELECT count(*) AS count FROM client_services'
    if where:
        raw = f"{raw} WHERE {' AND '.join(where)}"

    sql = text(raw)

    res = await session.execute(sql, params)
    count = res.scalar()

    return count
