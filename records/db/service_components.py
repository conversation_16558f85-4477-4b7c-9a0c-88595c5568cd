
from sqlalchemy import delete, update
from sqlalchemy.future import select

from records.db import base
from records.db import models


@base.session_aware()
async def create_service_component(values, session=None):
    return await base.create_model(models.ServiceComponent, values, session=session)


@base.session_aware()
async def update_service_component(service_component: models.ServiceComponent, new_values: dict, session=None):
    new_values['updated_at'] = base.now()
    update_q = update(models.ServiceComponent).where(models.ServiceComponent.id == service_component.id)
    update_q = update_q.values(new_values)

    await session.execute(update_q)

    service_component.update(new_values)

    return service_component


@base.session_aware()
async def delete_service_component(id: int, session=None):
    delete_q = delete(models.ServiceComponent).where(models.ServiceComponent.id == id)
    await session.execute(delete_q)


@base.session_aware()
async def delete_service_components(ids: list[int] = None, service_id: str = None, session=None):
    return await base.delete_by_x(models.ServiceComponent, id=ids, service_id=service_id, session=session)


@base.session_aware()
async def list_service_components(
    service_id: str = None,
    order: str = 'id',
    desc: bool = False,
    session=None
):
    query = select(models.ServiceComponent).where(models.ServiceComponent.service_id == service_id)
    if not hasattr(models.ServiceComponent, order):
        # Set default order
        order = 'id'
    order_col = getattr(models.ServiceComponent, order)

    if desc:
        order_col = order_col.desc()
    query = query.order_by(order_col)
    res = await session.execute(query)

    return res.scalars().fetchall()


# Client service components
@base.session_aware()
async def create_client_service_component(values, session=None):
    return await base.create_model(models.ClientServiceComponent, values, session=session)


@base.session_aware()
async def update_client_service_component(
    client_service_component: models.ClientServiceComponent, new_values: dict, session=None
):
    new_values['updated_at'] = base.now()
    update_q = update(models.ClientServiceComponent).where(models.ClientServiceComponent.id == client_service_component.id)
    update_q = update_q.values(new_values)

    await session.execute(update_q)

    client_service_component.update(new_values)

    return client_service_component


@base.session_aware()
async def delete_client_service_component(id: int, session=None):
    delete_q = delete(models.ClientServiceComponent).where(models.ClientServiceComponent.id == id)
    await session.execute(delete_q)


@base.session_aware()
async def delete_client_service_components(ids: list[int] = None, client_service_id: int = None, session=None):
    return await base.delete_by_x(models.ClientServiceComponent, id=ids, client_service_id=client_service_id, session=session)


@base.session_aware()
async def list_client_service_components(
    client_service_id: int,
    order: str = 'id',
    desc: bool = False,
    session=None
):
    query = select(models.ClientServiceComponent).where(models.ClientServiceComponent.client_service_id == client_service_id)
    if not hasattr(models.ClientServiceComponent, order):
        # Set default order
        order = 'id'
    order_col = getattr(models.ClientServiceComponent, order)

    if desc:
        order_col = order_col.desc()
    query = query.order_by(order_col)
    res = await session.execute(query)

    return res.scalars().fetchall()
