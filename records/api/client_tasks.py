import os
import datetime
from typing import Optional

from fastapi import APIRouter, HTTPException
from pydantic import BaseModel, field_validator

from records.utils import json_utils

from records.db import api as db_api


class ClientTaskCreate(BaseModel):
    description: Optional[str] = None
    date: Optional[datetime.datetime] = None
    name: str
    due_date: Optional[datetime.datetime] = None
    status: Optional[str] = 'OPEN'  # OPEN, IN_PROGRESS, COMPLETED
    client_service_id: Optional[int] = None
    tax_report_id: Optional[int] = None
    manager_id: Optional[str] = None

    _date_validator = field_validator(
        *['date', 'due_date'],
        mode='before'
    )(json_utils.date_validator)


class ClientTaskUpdate(BaseModel):
    description: Optional[str] = None
    date: Optional[datetime.datetime] = None
    name: Optional[str] = None
    due_date: Optional[datetime.datetime] = None
    status: Optional[str] = None
    client_service_id: Optional[int] = None
    tax_report_id: Optional[int] = None
    manager_id: Optional[str] = None

    _date_validator = field_validator(
        *['date', 'due_date'],
        mode='before'
    )(json_utils.date_validator)


def get_router(prefix='/'):
    router = APIRouter(prefix=os.path.join(prefix, 'tasks'))

    router.add_api_route("", list_client_tasks, methods=['GET'], name='List client tasks')
    router.add_api_route("", create_client_task, methods=['POST'], name='Create client task')
    router.add_api_route("/{task_id}", get_client_task, methods=['GET'], name='Get client task')
    router.add_api_route("/{task_id}", update_client_task, methods=['PUT'], name='Update client task')
    router.add_api_route("/{task_id}", delete_client_task, methods=['DELETE'], name='Delete client task')

    return router


async def list_client_tasks(
    client_id: str,
    order: str = 'id',
    desc: bool = False,
    # session: AsyncSession = Depends(get_session)
):
    tasks = await db_api.list_client_tasks(client_id=client_id, order=order, desc=desc)
    return {'items': tasks}


async def get_client_task(client_id: str, task_id: int):
    client_task_db = await db_api.get_client_task_by_id(task_id)
    if not client_task_db:
        raise HTTPException(status_code=404, detail="Client task not found")
    if client_task_db.client_id != client_id:
        raise HTTPException(status_code=403, detail="Task does not belong to this client")
    return client_task_db


async def create_client_task(client_id: str, task: ClientTaskCreate):
    client_task_dict = task.model_dump(exclude_unset=True)
    client_task_dict['client_id'] = client_id

    db_client_task = await db_api.create_client_task(client_task_dict)
    return db_client_task


async def update_client_task(client_id: str, task_id: int, task: ClientTaskUpdate):
    client_task_db = await db_api.get_client_task_by_id(task_id)
    if not client_task_db:
        raise HTTPException(status_code=404, detail="Client task not found")
    if client_task_db.client_id != client_id:
        raise HTTPException(status_code=403, detail="Task does not belong to this client")

    updated_task = await db_api.update_client_task(client_task_db, task.model_dump(exclude_unset=True))
    return updated_task


async def delete_client_task(client_id: str, task_id: int):
    client_task_db = await db_api.get_client_task_by_id(task_id)
    if not client_task_db:
        raise HTTPException(status_code=404, detail="Client task not found")
    if client_task_db.client_id != client_id:
        raise HTTPException(status_code=403, detail="Task does not belong to this client")

    await db_api.delete_client_task(task_id)
    return None
