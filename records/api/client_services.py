import datetime
import os
from typing import Optional, List

from fastapi import APIRouter, HTTPException
from fastapi import Response
from pydantic import BaseModel, field_validator

from records.db import api as db_api
from records.db import base
from records.db.models import ServicePriceType
from records.utils import json_utils


class ClientServiceComponentCreate(BaseModel):
    service_component_id: int
    custom_price: Optional[str] = None


class ServiceID(BaseModel):
    id: str


class ClientServiceCreate(BaseModel):
    service_id: Optional[str] = None
    service: Optional[ServiceID] = None
    start_date: Optional[datetime.datetime] = None
    end_date: Optional[datetime.datetime] = None
    note: Optional[str] = None
    discount_percent: Optional[int] = None
    discount_amount: Optional[str] = None
    total: Optional[str] = None
    components: Optional[List[ClientServiceComponentCreate]] = None

    _date_validator = field_validator(
        *['start_date', 'end_date'],
        mode='before'
    )(json_utils.date_validator)
    

class ClientServiceUpdate(BaseModel):
    start_date: Optional[datetime.datetime] = None
    end_date: Optional[datetime.datetime] = None
    note: Optional[str] = None
    discount_percent: Optional[int] = None
    discount_amount: Optional[str] = None
    total: Optional[str] = None

    _date_validator = field_validator(
        *['start_date', 'end_date'],
        mode='before'
    )(json_utils.date_validator)


def get_router(prefix='/'):
    router = APIRouter(prefix=os.path.join(prefix, 'services'))

    router.add_api_route("", list_services, methods=['GET'], name='List client services')
    router.add_api_route("", create_client_service, methods=['POST'], name='Create client service')
    router.add_api_route("/{service_id}", get_client_service, methods=['GET'], name='Get client service')
    router.add_api_route("/{service_id}", update_client_service, methods=['PUT'], name='Update client service')
    router.add_api_route("/{service_id}", delete_client_service, methods=['DELETE'], name='Delete client service')

    return router


async def list_services(
    client_id: str,
    # session: AsyncSession = Depends(get_session)
):
    services = await db_api.list_client_services(client_id=client_id)
    return {'items': services}


async def get_client_service(client_id: str, service_id: int):
    client_service_db = await db_api.get_client_service_by_id(service_id)
    return client_service_db.to_dict()


async def create_client_service(client_id: str, service: ClientServiceCreate):
    # Check if service exists
    service_id = service.service_id or service.service.id
    service_db = await db_api.get_service_by_id(service_id)
    if not service_db:
        raise HTTPException(400, f'Service id={service_id} does not exist')

    client_service_dict = service.model_dump(exclude_unset=True)
    # Set start date to today if not set
    if not client_service_dict.get('start_date'):
        client_service_dict['start_date'] = datetime.datetime.now(datetime.timezone.utc)

    # Check discount
    if client_service_dict.get('discount_percent') and client_service_dict.get('discount_amount'):
        raise HTTPException(400, 'Cannot specify both discount_percent and discount_amount')

    # Calculate total with discount if applicable
    if client_service_dict.get('discount_percent'):
        client_service_dict['total'] = f"{service_db.price * (1 - client_service_dict['discount_percent'] / 100):.2f}"
    elif client_service_dict.get('discount_amount'):
        # Discount amount cannot be more than service price
        if float(client_service_dict['discount_amount']) > service_db.price:
            raise HTTPException(400, f'Discount amount cannot be more than service price {service_db.price}')
        client_service_dict['total'] = f"{service_db.price - float(client_service_dict['discount_amount']):.2f}"
    else:
        client_service_dict['total'] = f"{service_db.price:.2f}"

    client_service_dict['client_id'] = client_id
    async with base.session_context() as session:
        db_client_service = await db_api.create_client_service(client_service_dict)
        # Create necessary tasks and assign to manager

    return db_client_service.to_dict()


async def update_client_service(client_id: str, service_id: int, service: ClientServiceUpdate):
    client_service_db = await db_api.get_client_service_by_id(service_id)
    updated_client = await db_api.update_client_service(client_service_db, service.model_dump())
    return updated_client.to_dict()


async def delete_client_service(client_id: str, service_id: int, delete_tasks: bool = False):
    client_service_db = await db_api.get_client_service_by_id(service_id)
    if delete_tasks:
        await db_api.delete_client_tasks(client_id=client_id, client_service_id=service_id)
    await db_api.delete_client_service(service_id)
    return Response(status_code=204)
